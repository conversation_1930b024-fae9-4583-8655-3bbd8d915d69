// ===== SISTEMA DE CONFIGURAÇÕES DINÂMICAS - ESTÚDIO730 =====

/**
 * Gerenciador de Configurações
 * Responsável por gerenciar todas as configurações da página
 */
class ConfigManager {
    constructor() {
        this.storageKey = 'estudio730_config';
        this.defaultConfig = this.getDefaultConfig();
        this.currentConfig = this.loadConfig();
        this.init();
    }

    /**
     * Configuração padrão do sistema
     */
    getDefaultConfig() {
        return {
            links: [
                {
                    id: 'whatsapp',
                    name: 'WhatsApp',
                    url: 'https://wa.me/5511999999999?text=Olá! Gostaria de agendar um horário no Estúdio730.',
                    icon: 'fab fa-whatsapp',
                    color: '#25d366',
                    visible: true,
                    removable: false,
                    order: 1
                },
                {
                    id: 'instagram',
                    name: 'Instagram',
                    url: 'https://www.instagram.com/estudio730/',
                    icon: 'fab fa-instagram',
                    color: '#e4405f',
                    visible: true,
                    removable: false,
                    order: 2
                },
                {
                    id: 'location',
                    name: 'Localização',
                    url: 'https://www.google.com/maps/search/?api=1&query=Rua das Flores, 123, São Paulo, SP',
                    icon: 'fas fa-map-marker-alt',
                    color: '#4285f4',
                    visible: true,
                    removable: false,
                    order: 3
                },
                {
                    id: 'website',
                    name: 'Site Oficial',
                    url: 'https://www.estudio730.com.br',
                    icon: 'fas fa-globe',
                    color: '#6c5ce7',
                    visible: true,
                    removable: false,
                    order: 4
                }
            ],
            settings: {
                lastModified: Date.now(),
                version: '1.0.0'
            }
        };
    }

    /**
     * Inicializa o sistema de configurações
     */
    init() {
        this.bindEvents();
        this.renderLinks();
        this.initMobileOptimizations();
        console.log('🔧 Sistema de Configurações do Estúdio730 inicializado');
    }

    /**
     * Vincula eventos aos elementos da interface
     */
    bindEvents() {
        // Botão de abrir configurações
        const configButton = document.getElementById('config-button');
        configButton?.addEventListener('click', () => this.openModal());

        // Suporte para navegação por teclado no botão de configuração
        configButton?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.openModal();
            }
        });

        // Botão de fechar modal
        const closeButton = document.getElementById('config-close');
        closeButton?.addEventListener('click', () => this.closeModal());

        // Fechar modal clicando fora
        const modal = document.getElementById('config-modal');
        modal?.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Tecla ESC para fechar modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal?.classList.contains('active')) {
                this.closeModal();
            }
        });

        // Botão expandir formulário
        const expandButton = document.getElementById('btn-expand-form');
        expandButton?.addEventListener('click', () => this.toggleAddForm());

        // Botão cancelar formulário
        const cancelFormButton = document.getElementById('btn-cancel-form');
        cancelFormButton?.addEventListener('click', () => this.hideAddForm());

        // Formulário de adicionar link
        const addForm = document.getElementById('add-link-form');
        addForm?.addEventListener('submit', (e) => this.handleAddLink(e));

        // Modal de edição
        const editCloseButton = document.getElementById('edit-close');
        editCloseButton?.addEventListener('click', () => this.closeEditModal());

        const cancelEditButton = document.getElementById('btn-cancel-edit');
        cancelEditButton?.addEventListener('click', () => this.closeEditModal());

        const editForm = document.getElementById('edit-link-form');
        editForm?.addEventListener('submit', (e) => this.handleEditLink(e));

        // Fechar modal de edição clicando fora
        const editModal = document.getElementById('edit-modal');
        editModal?.addEventListener('click', (e) => {
            if (e.target === editModal) this.closeEditModal();
        });

        // Tecla ESC para fechar modal de edição
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && editModal?.classList.contains('active')) {
                this.closeEditModal();
            }
        });

        // Seletores de ícones
        const iconSelect = document.getElementById('link-icon');
        iconSelect?.addEventListener('change', () => this.updateIconPreview('icon-preview', iconSelect));

        const editIconSelect = document.getElementById('edit-link-icon');
        editIconSelect?.addEventListener('change', () => this.updateIconPreview('edit-icon-preview', editIconSelect));



        // Botões do footer
        document.getElementById('btn-save')?.addEventListener('click', () => this.saveConfig());
        document.getElementById('btn-cancel')?.addEventListener('click', () => this.closeModal());
        document.getElementById('btn-restore')?.addEventListener('click', () => this.restoreDefaults());
    }

    /**
     * Abre o modal de configurações
     */
    openModal() {
        const modal = document.getElementById('config-modal');
        const configButton = document.getElementById('config-button');

        modal?.classList.add('active');
        configButton?.setAttribute('aria-expanded', 'true');
        this.renderConfigLinks();
        document.body.style.overflow = 'hidden';

        // Inicializar efeitos Magic UI do modal
        setTimeout(() => {
            this.initModalMagicEffects();
        }, 100);
    }

    /**
     * Fecha o modal de configurações
     */
    closeModal() {
        const modal = document.getElementById('config-modal');
        const modalContent = document.querySelector('.config-modal-content');
        const configButton = document.getElementById('config-button');

        if (this.isMobile()) {
            // Animação de saída para mobile
            modal?.classList.add('closing');
            modalContent?.classList.add('closing');

            setTimeout(() => {
                modal?.classList.remove('active', 'closing');
                modalContent?.classList.remove('closing');
                configButton?.setAttribute('aria-expanded', 'false');
                document.body.style.overflow = '';
                this.hideAddForm();
                this.clearForm();
            }, 300);
        } else {
            // Fechamento normal para desktop
            modal?.classList.remove('active');
            configButton?.setAttribute('aria-expanded', 'false');
            document.body.style.overflow = '';
            this.hideAddForm();
            this.clearForm();
        }
    }

    /**
     * Alterna exibição do formulário de adicionar link
     */
    toggleAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        if (container?.classList.contains('expanded')) {
            this.hideAddForm();
        } else {
            this.showAddForm();
        }
    }

    /**
     * Mostra o formulário de adicionar link
     */
    showAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        container?.classList.add('expanded');
        button?.classList.add('hidden');

        // Foca no primeiro campo após a animação
        setTimeout(() => {
            document.getElementById('link-name')?.focus();
        }, 300);
    }

    /**
     * Esconde o formulário de adicionar link
     */
    hideAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        container?.classList.remove('expanded');
        button?.classList.remove('hidden');
        this.clearForm();
    }

    /**
     * Atualiza preview do ícone selecionado
     */
    updateIconPreview(previewId, selectElement) {
        const preview = document.getElementById(previewId);

        if (!preview || !selectElement) return;

        const selectedOption = selectElement.selectedOptions[0];
        const iconClass = selectElement.value;
        const suggestedColor = selectedOption?.dataset.color;

        if (iconClass) {
            // Atualiza o ícone no preview usando SVG com cor padrão
            const iconSVG = this.getIconSVG(iconClass, suggestedColor);
            preview.innerHTML = iconSVG;
            preview.classList.add('active');
        } else {
            // Reseta preview se nenhum ícone selecionado
            preview.innerHTML = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
            </svg>`;
            preview.classList.remove('active');
        }
    }

    /**
     * Carrega configurações do localStorage
     */
    loadConfig() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const config = JSON.parse(saved);
                // Mescla com configuração padrão para garantir compatibilidade
                return this.mergeConfigs(this.defaultConfig, config);
            }
        } catch (error) {
            console.warn('Erro ao carregar configurações:', error);
        }
        return { ...this.defaultConfig };
    }

    /**
     * Salva configurações no localStorage
     */
    saveConfig() {
        try {
            this.currentConfig.settings.lastModified = Date.now();
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfig));
            this.renderLinks();
            this.showToast('✅ Configurações salvas com sucesso!', 'success');
            this.closeModal();
            console.log('💾 Configurações salvas:', this.currentConfig);
        } catch (error) {
            console.error('Erro ao salvar configurações:', error);
            this.showToast('❌ Erro ao salvar configurações', 'error');
        }
    }

    /**
     * Salva configurações silenciosamente (sem toast ou fechar modal)
     */
    saveConfigSilently() {
        try {
            this.currentConfig.settings.lastModified = Date.now();
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfig));
            console.log('💾 Configurações salvas automaticamente');
        } catch (error) {
            console.error('Erro ao salvar configurações automaticamente:', error);
        }
    }

    /**
     * Restaura configurações padrão
     */
    restoreDefaults() {
        if (confirm('Tem certeza que deseja restaurar as configurações padrão? Todos os links personalizados serão perdidos.')) {
            this.currentConfig = { ...this.defaultConfig };
            this.saveConfig();
            this.renderConfigLinks();
            this.showToast('🔄 Configurações restauradas para o padrão', 'info');
        }
    }

    /**
     * Restaura apenas a ordem padrão dos links
     */
    restoreDefaultOrder() {
        const defaultLinks = this.getDefaultConfig().links;

        // Atualiza a ordem dos links padrão para a ordem original
        this.currentConfig.links.forEach(link => {
            const defaultLink = defaultLinks.find(dl => dl.id === link.id);
            if (defaultLink) {
                link.order = defaultLink.order;
            }
        });

        this.renderConfigLinks();
        this.showToast('🔄 Ordem dos links restaurada', 'info');
    }

    /**
     * Abre modal de edição de link
     */
    openEditModal(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (!link) return;

        // Preenche os campos do formulário
        document.getElementById('edit-link-id').value = link.id;
        document.getElementById('edit-link-name').value = link.name;
        document.getElementById('edit-link-url').value = link.url;
        document.getElementById('edit-link-icon').value = link.icon;

        // Atualiza preview do ícone
        const editIconSelect = document.getElementById('edit-link-icon');
        this.updateIconPreview('edit-icon-preview', editIconSelect);

        // Mostra o modal
        const modal = document.getElementById('edit-modal');
        modal?.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Inicializar efeitos Magic UI do modal de edição
        setTimeout(() => {
            this.initEditModalMagicEffects();
            document.getElementById('edit-link-name')?.focus();
        }, 300);
    }

    /**
     * Inicializa efeitos Magic UI no modal de edição
     */
    initEditModalMagicEffects() {
        const editModal = document.querySelector('.edit-modal-content');
        if (!editModal) return;

        // Efeito spotlight
        editModal.addEventListener('mousemove', (e) => {
            const rect = editModal.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            editModal.style.setProperty('--mouse-x', `${x}%`);
            editModal.style.setProperty('--mouse-y', `${y}%`);
        });

        // Efeitos ripple para botões
        const buttons = editModal.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }

    /**
     * Fecha modal de edição
     */
    closeEditModal() {
        const modal = document.getElementById('edit-modal');
        modal?.classList.remove('active');
        document.body.style.overflow = '';
        this.clearEditForm();
    }

    /**
     * Limpa formulário de edição
     */
    clearEditForm() {
        const form = document.getElementById('edit-link-form');
        if (form) {
            form.reset();

            // Reseta preview do ícone
            const preview = document.getElementById('edit-icon-preview');
            if (preview) {
                preview.innerHTML = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                </svg>`;
                preview.classList.remove('active');
            }
        }
    }

    /**
     * Manipula edição de link
     */
    handleEditLink(e) {
        e.preventDefault();

        const linkId = document.getElementById('edit-link-id').value;
        const name = document.getElementById('edit-link-name').value.trim();
        const url = document.getElementById('edit-link-url').value.trim();
        const icon = document.getElementById('edit-link-icon').value || 'fas fa-link';

        // Obter cor padrão do ícone selecionado
        const selectedOption = document.getElementById('edit-link-icon').selectedOptions[0];
        const color = selectedOption?.dataset.color || '#6c5ce7';

        // Validação
        if (!name || !url || !icon) {
            this.showToast('❌ Nome, URL e Ícone são obrigatórios', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('❌ URL inválida', 'error');
            return;
        }

        // Encontra e atualiza o link
        const linkIndex = this.currentConfig.links.findIndex(l => l.id === linkId);
        if (linkIndex === -1) {
            this.showToast('❌ Link não encontrado', 'error');
            return;
        }

        // Atualiza o link
        this.currentConfig.links[linkIndex] = {
            ...this.currentConfig.links[linkIndex],
            name,
            url,
            icon,
            color
        };

        this.renderConfigLinks();
        this.closeEditModal();
        this.showToast(`✅ Link "${name}" atualizado com sucesso!`, 'success');
    }

    /**
     * Mescla configurações
     */
    mergeConfigs(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        if (userConfig.links) {
            // Mantém links padrão e adiciona personalizados
            const defaultLinks = defaultConfig.links;
            const userLinks = userConfig.links;
            
            merged.links = defaultLinks.map(defaultLink => {
                const userLink = userLinks.find(ul => ul.id === defaultLink.id);
                return userLink ? { ...defaultLink, ...userLink } : defaultLink;
            });

            // Adiciona links personalizados
            const customLinks = userLinks.filter(ul => !defaultLinks.some(dl => dl.id === ul.id));
            merged.links.push(...customLinks);
        }

        if (userConfig.settings) {
            merged.settings = { ...merged.settings, ...userConfig.settings };
        }

        return merged;
    }

    /**
     * Renderiza os links na página principal
     */
    renderLinks() {
        const linksSection = document.querySelector('.links-section');
        if (!linksSection) return;

        // Limpa links existentes (exceto os elementos que não são botões)
        const existingButtons = linksSection.querySelectorAll('.link-button');
        existingButtons.forEach(btn => btn.remove());

        // Ordena links por ordem e visibilidade
        const visibleLinks = this.currentConfig.links
            .filter(link => link.visible)
            .sort((a, b) => a.order - b.order);

        // Renderiza cada link
        visibleLinks.forEach(link => {
            const linkElement = this.createLinkElement(link);
            linksSection.appendChild(linkElement);
        });
    }

    /**
     * Cria elemento de link para a página principal
     */
    createLinkElement(link) {
        const linkEl = document.createElement('a');
        linkEl.href = link.url;
        linkEl.className = `link-button custom-link`;
        linkEl.target = '_blank';
        linkEl.rel = 'noopener noreferrer';
        linkEl.style.setProperty('--link-color', link.color);

        // Mapear ícones Font Awesome para SVGs
        const iconSVG = this.getIconSVG(link.icon, link.color);
        const arrowSVG = `<svg class="icon-svg arrow" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>`;

        linkEl.innerHTML = `
            <div class="button-content">
                ${iconSVG}
                <span class="button-text">
                    <strong>${link.name}</strong>
                    <small>Clique para acessar</small>
                </span>
            </div>
            ${arrowSVG}
        `;

        // Adiciona estilos dinâmicos
        linkEl.addEventListener('mouseenter', () => {
            linkEl.style.borderColor = link.color;
            linkEl.style.boxShadow = `0 15px 40px ${link.color}30`;
        });

        linkEl.addEventListener('mouseleave', () => {
            linkEl.style.borderColor = 'transparent';
            linkEl.style.boxShadow = 'var(--shadow)';
        });

        return linkEl;
    }

    /**
     * Mapeia ícones Font Awesome para SVGs
     */
    getIconSVG(iconClass, color) {
        const iconMap = {
            'fab fa-whatsapp': {
                path: 'M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488',
                defaultColor: '#25d366'
            },
            'fab fa-instagram': {
                path: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z',
                defaultColor: '#e4405f'
            },
            'fab fa-facebook': {
                path: 'M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z',
                defaultColor: '#1877f2'
            },
            'fab fa-twitter': {
                path: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z',
                defaultColor: '#1da1f2'
            },
            'fab fa-tiktok': {
                path: 'M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z',
                defaultColor: '#000000'
            },
            'fab fa-youtube': {
                path: 'M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z',
                defaultColor: '#ff0000'
            },
            'fab fa-linkedin': {
                path: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z',
                defaultColor: '#0077b5'
            },
            'fab fa-telegram': {
                path: 'M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z',
                defaultColor: '#0088cc'
            },
            'fas fa-envelope': {
                path: 'M0 4a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2 0v.217l10 6.25 10-6.25V4H2zm0 2.435V20h20V6.435l-10 6.25L2 6.435z',
                defaultColor: '#ea4335'
            },
            'fas fa-phone': {
                path: 'M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z',
                defaultColor: '#34a853'
            },
            'fas fa-globe': {
                path: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z',
                defaultColor: '#6c5ce7'
            },
            'fas fa-map-marker-alt': {
                path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
                defaultColor: '#4285f4'
            },
            'fas fa-calendar': {
                path: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z',
                defaultColor: '#fbbc04'
            },
            'fas fa-shopping-cart': {
                path: 'M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z',
                defaultColor: '#ff6900'
            },
            'fas fa-link': {
                path: 'M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z',
                defaultColor: '#6c5ce7'
            }
        };

        const icon = iconMap[iconClass];
        if (!icon) {
            // Ícone padrão se não encontrado
            return `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true" style="fill: ${color};">
                <path d="M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H6.9C4.01 7 1.9 9.11 1.9 12s2.11 5 5 5h4v-1.9H6.9c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9.1-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.89 0 5-2.11 5-5s-2.11-5-5-5z"/>
            </svg>`;
        }

        const finalColor = color || icon.defaultColor;
        return `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true" style="fill: ${finalColor};">
            <path d="${icon.path}"/>
        </svg>`;
    }

    /**
     * Renderiza links no modal de configurações
     */
    renderConfigLinks() {
        const linksList = document.getElementById('links-list');
        if (!linksList) return;

        linksList.innerHTML = '';

        // Ordena links por ordem
        const sortedLinks = [...this.currentConfig.links].sort((a, b) => a.order - b.order);

        sortedLinks.forEach(link => {
            const linkItem = this.createConfigLinkItem(link);
            linksList.appendChild(linkItem);
        });

        // Aplica efeitos Magic UI aos novos elementos
        setTimeout(() => {
            this.initMagicCardEffects();
        }, 100);
    }

    /**
     * Cria item de link para o modal de configurações
     */
    createConfigLinkItem(link) {
        const item = document.createElement('div');
        item.className = 'link-item';
        item.dataset.linkId = link.id;

        // Determina a posição do link na lista ordenada
        const sortedLinks = [...this.currentConfig.links].sort((a, b) => a.order - b.order);
        const currentIndex = sortedLinks.findIndex(l => l.id === link.id);
        const isFirst = currentIndex === 0;
        const isLast = currentIndex === sortedLinks.length - 1;

        // Gerar SVGs para os ícones
        const linkIconSVG = this.getIconSVG(link.icon, '#ffffff'); // Branco para contraste no preview
        const editIconSVG = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
        </svg>`;
        const upIconSVG = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
        </svg>`;
        const downIconSVG = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
        </svg>`;
        const trashIconSVG = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
        </svg>`;

        item.innerHTML = `
            <div class="link-item-header">
                <div class="link-preview" style="background-color: ${link.color}">
                    ${linkIconSVG}
                </div>
                <div class="link-info">
                    <div class="link-details">
                        <h4>${link.name}</h4>
                        <small>${this.truncateUrl(link.url)}</small>
                    </div>
                    <div class="link-controls">
                        <div class="toggle-switch ${link.visible ? 'active' : ''}"
                             onclick="configManager.toggleLinkVisibility('${link.id}')">
                        </div>
                        <button class="control-btn edit" onclick="configManager.openEditModal('${link.id}')"
                                title="Editar link">
                            ${editIconSVG}
                        </button>
                        <button class="control-btn ${isFirst ? 'disabled' : ''}"
                                onclick="${isFirst ? '' : `configManager.moveLink('${link.id}', 'up')`}"
                                title="${isFirst ? 'Já está no topo' : 'Mover para cima'}"
                                ${isFirst ? 'disabled' : ''}>
                            ${upIconSVG}
                        </button>
                        <button class="control-btn ${isLast ? 'disabled' : ''}"
                                onclick="${isLast ? '' : `configManager.moveLink('${link.id}', 'down')`}"
                                title="${isLast ? 'Já está no final' : 'Mover para baixo'}"
                                ${isLast ? 'disabled' : ''}>
                            ${downIconSVG}
                        </button>
                        ${link.removable ? `
                            <button class="control-btn danger" onclick="configManager.removeLink('${link.id}')"
                                    title="Remover link">
                                ${trashIconSVG}
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        return item;
    }

    /**
     * Trunca URL para exibição
     */
    truncateUrl(url) {
        return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }

    /**
     * Alterna visibilidade do link
     */
    toggleLinkVisibility(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (link) {
            link.visible = !link.visible;
            this.renderConfigLinks();
            this.renderLinks(); // Atualiza também a página principal
            this.saveConfigSilently(); // Salva automaticamente

            const status = link.visible ? 'visível' : 'oculto';
            this.showToast(`👁️ Link "${link.name}" agora está ${status}`, 'info');
        }
    }

    /**
     * Move link para cima ou para baixo
     */
    moveLink(linkId, direction) {
        const links = this.currentConfig.links;

        // Ordena os links por order para trabalhar com a ordem correta
        const sortedLinks = [...links].sort((a, b) => a.order - b.order);
        const currentIndex = sortedLinks.findIndex(l => l.id === linkId);

        if (currentIndex === -1) return;

        // Calcula o novo índice
        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        // Validação de limites
        if (newIndex < 0 || newIndex >= sortedLinks.length) {
            const message = direction === 'up'
                ? '❌ Este item já está no topo da lista'
                : '❌ Este item já está no final da lista';
            this.showToast(message, 'warning');
            return;
        }

        // Remove o item da posição atual
        const [movedLink] = sortedLinks.splice(currentIndex, 1);

        // Insere na nova posição
        sortedLinks.splice(newIndex, 0, movedLink);

        // Recalcula todas as ordens sequencialmente
        sortedLinks.forEach((link, index) => {
            link.order = index + 1;
        });

        // Atualiza o array original com as novas ordens
        this.currentConfig.links = sortedLinks;

        // Atualiza ambas as renderizações e salva automaticamente
        this.renderConfigLinks();
        this.renderLinks();
        this.saveConfigSilently();

        // Feedback visual de sucesso
        const direction_text = direction === 'up' ? 'para cima' : 'para baixo';
        this.showToast(`✅ Link movido ${direction_text}`, 'success');
    }

    /**
     * Remove link personalizado
     */
    removeLink(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        
        if (!link || !link.removable) {
            this.showToast('❌ Este link não pode ser removido', 'error');
            return;
        }

        if (confirm(`Tem certeza que deseja remover o link "${link.name}"?`)) {
            this.currentConfig.links = this.currentConfig.links.filter(l => l.id !== linkId);
            this.renderConfigLinks();
            this.showToast(`🗑️ Link "${link.name}" removido`, 'info');
        }
    }

    /**
     * Manipula adição de novo link
     */
    handleAddLink(e) {
        e.preventDefault();
        
        const name = document.getElementById('link-name').value.trim();
        const url = document.getElementById('link-url').value.trim();
        const icon = document.getElementById('link-icon').value || 'fas fa-link';

        // Obter cor padrão do ícone selecionado
        const selectedOption = document.getElementById('link-icon').selectedOptions[0];
        const color = selectedOption?.dataset.color || '#6c5ce7';

        // Validação
        if (!name || !url || !icon) {
            this.showToast('❌ Nome, URL e Ícone são obrigatórios', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('❌ URL inválida', 'error');
            return;
        }

        // Calcula o próximo order baseado no maior valor existente
        const maxOrder = this.currentConfig.links.length > 0
            ? Math.max(...this.currentConfig.links.map(l => l.order))
            : 0;

        // Cria novo link
        const newLink = {
            id: this.generateId(),
            name,
            url,
            icon,
            color,
            visible: true,
            removable: true,
            order: maxOrder + 1
        };

        this.currentConfig.links.push(newLink);
        this.renderConfigLinks();
        this.renderLinks(); // Atualiza também a página principal
        this.hideAddForm();
        this.showToast(`✅ Link "${name}" adicionado com sucesso!`, 'success');
    }

    /**
     * Valida URL
     */
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * Gera ID único
     */
    generateId() {
        return 'link_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Limpa formulário
     */
    clearForm() {
        const form = document.getElementById('add-link-form');
        if (form) {
            form.reset();

            // Reseta preview do ícone
            const preview = document.getElementById('icon-preview');
            if (preview) {
                preview.innerHTML = `<svg class="icon-svg" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                </svg>`;
                preview.classList.remove('active');
            }
        }
    }

    /**
     * Mostra notificação toast
     */
    showToast(message, type = 'info') {
        // Remove toast anterior se existir
        const existingToast = document.querySelector('.config-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Cria novo toast
        const toast = document.createElement('div');
        toast.className = `config-toast ${type}`;
        toast.textContent = message;

        // Estilos do toast
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        toast.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 3000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            font-size: 0.9rem;
        `;

        document.body.appendChild(toast);

        // Remove toast após 4 segundos
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    /**
     * Inicializa efeitos Magic UI no modal
     */
    initModalMagicEffects() {
        this.initSpotlightEffect();
        this.initRippleEffects();
        this.initMagicCardEffects();
    }

    /**
     * Efeito spotlight que segue o mouse no modal
     */
    initSpotlightEffect() {
        const modalContent = document.querySelector('.config-modal-content');
        if (!modalContent) return;

        modalContent.addEventListener('mousemove', (e) => {
            const rect = modalContent.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            modalContent.style.setProperty('--mouse-x', `${x}%`);
            modalContent.style.setProperty('--mouse-y', `${y}%`);
        });
    }

    /**
     * Efeitos ripple para botões do modal
     */
    initRippleEffects() {
        const buttons = document.querySelectorAll('.config-modal button, .edit-modal button');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }

    /**
     * Efeitos Magic Card para os itens de links
     */
    initMagicCardEffects() {
        const linkItems = document.querySelectorAll('.link-item');

        linkItems.forEach(item => {
            item.addEventListener('mousemove', (e) => {
                const rect = item.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;

                item.style.setProperty('--mouse-x', `${x}%`);
                item.style.setProperty('--mouse-y', `${y}%`);
            });
        });
    }

    /**
     * Cria efeito ripple
     */
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 10;
        `;

        // Garante que o elemento pai tenha position relative
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * Inicializa otimizações para mobile
     */
    initMobileOptimizations() {
        this.setupTabNavigation();
        this.setupSwipeGestures();
        this.setupRealTimeValidation();
        this.optimizePerformanceForMobile();
        this.setupTouchOptimizations();
    }

    /**
     * Configura navegação por abas
     */
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');
        let isTransitioning = false;

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Prevenir múltiplas transições simultâneas
                if (isTransitioning) return;

                const targetTab = button.dataset.tab;
                const targetPanel = document.getElementById(`${targetTab}-panel`);

                // Se já está ativo, não fazer nada
                if (button.classList.contains('active')) return;

                isTransitioning = true;

                // Usar requestAnimationFrame para otimizar mudanças de DOM
                requestAnimationFrame(() => {
                    // Remove active de todos os botões e painéis
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));

                    // Adiciona active ao botão clicado
                    button.classList.add('active');

                    // Mostra o painel correspondente
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                        // Reset scroll position imediatamente para evitar jump
                        targetPanel.scrollTop = 0;
                    }

                    // Feedback haptic em dispositivos suportados
                    if (navigator.vibrate) {
                        navigator.vibrate(10);
                    }

                    // Liberar flag após animação
                    setTimeout(() => {
                        isTransitioning = false;
                    }, 300);
                });
            });
        });
    }

    /**
     * Configura gestos de swipe para fechar modal
     */
    setupSwipeGestures() {
        if (!this.isMobile()) return;

        const modal = document.querySelector('.config-modal-content');
        const swipeIndicator = document.querySelector('.swipe-handle');
        let startY = 0;
        let currentY = 0;
        let isDragging = false;

        const handleTouchStart = (e) => {
            startY = e.touches[0].clientY;
            isDragging = true;
            modal.style.transition = 'none';
        };

        const handleTouchMove = (e) => {
            if (!isDragging) return;

            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;

            // Só permite swipe para baixo
            if (deltaY > 0) {
                const progress = Math.min(deltaY / 200, 1);
                modal.style.transform = `translateY(${deltaY}px)`;
                modal.style.opacity = 1 - (progress * 0.3);

                // Feedback visual no indicador
                if (swipeIndicator) {
                    swipeIndicator.style.width = `${40 + (progress * 20)}px`;
                    swipeIndicator.style.background = progress > 0.5 ?
                        'var(--accent-color)' : 'rgba(255, 255, 255, 0.3)';
                }
            }
        };

        const handleTouchEnd = () => {
            if (!isDragging) return;
            isDragging = false;

            const deltaY = currentY - startY;
            modal.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

            if (deltaY > 100) {
                // Fechar modal
                this.closeModal();
            } else {
                // Voltar à posição original
                modal.style.transform = 'translateY(0)';
                modal.style.opacity = '1';
            }

            // Reset do indicador
            if (swipeIndicator) {
                swipeIndicator.style.width = '40px';
                swipeIndicator.style.background = 'rgba(255, 255, 255, 0.3)';
            }
        };

        modal?.addEventListener('touchstart', handleTouchStart, { passive: true });
        modal?.addEventListener('touchmove', handleTouchMove, { passive: true });
        modal?.addEventListener('touchend', handleTouchEnd, { passive: true });
    }

    /**
     * Configura validação em tempo real
     */
    setupRealTimeValidation() {
        const urlInputs = document.querySelectorAll('input[type="url"]');

        urlInputs.forEach(input => {
            let validationTimeout;

            input.addEventListener('input', (e) => {
                clearTimeout(validationTimeout);

                // Debounce para evitar muitas validações
                validationTimeout = setTimeout(() => {
                    this.validateUrlInput(input);
                }, 500);
            });
        });
    }

    /**
     * Valida input de URL em tempo real
     */
    validateUrlInput(input) {
        const value = input.value.trim();
        const isValid = value === '' || this.isValidUrl(value);

        // Remove classes anteriores
        input.classList.remove('valid', 'invalid');

        if (value !== '') {
            input.classList.add(isValid ? 'valid' : 'invalid');

            // Feedback visual
            if (isValid) {
                input.style.borderColor = '#27ae60';
                input.style.boxShadow = '0 0 0 2px rgba(39, 174, 96, 0.2)';
            } else {
                input.style.borderColor = '#e74c3c';
                input.style.boxShadow = '0 0 0 2px rgba(231, 76, 60, 0.2)';
            }
        } else {
            input.style.borderColor = '';
            input.style.boxShadow = '';
        }
    }

    /**
     * Otimiza performance para dispositivos móveis
     */
    optimizePerformanceForMobile() {
        if (!this.isMobile()) return;

        // Reduz efeitos Magic UI em mobile
        const modal = document.querySelector('.config-modal-content');
        if (modal) {
            modal.style.setProperty('--reduce-effects', '1');
        }

        // Desabilita animações pesadas em dispositivos lentos
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.body.classList.add('reduce-animations');
        }
    }

    /**
     * Configura otimizações de touch
     */
    setupTouchOptimizations() {
        if (!this.isMobile()) return;

        // Adiciona classe para otimizações touch
        document.body.classList.add('touch-device');

        // Melhora feedback de toque
        const touchElements = document.querySelectorAll('button, .link-item, .toggle-switch');

        touchElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.style.transform = 'scale(0.98)';
            }, { passive: true });

            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.style.transform = '';
                }, 100);
            }, { passive: true });
        });
    }

    /**
     * Detecta se é dispositivo móvel
     */
    isMobile() {
        return window.innerWidth <= 767 ||
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
}

// Adiciona estilos de animação para o toast
const toastStyles = document.createElement('style');
toastStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(toastStyles);

// Inicializa o sistema quando o DOM estiver carregado
let configManager;
document.addEventListener('DOMContentLoaded', () => {
    configManager = new ConfigManager();
});
